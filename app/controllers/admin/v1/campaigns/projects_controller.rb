# frozen_string_literal: true

class Admin::V1::Campaigns::ProjectsController < Admin::V1::BaseController
  before_action -> { authorize! with: Admin::V1::LecuponerPolicy }
  before_action :set_campaign

  def index
    @projects =
      @campaign
        .businesses
        .active
        .order(:name)
        .page(params[:page])

    render json: @projects,
      each_serializer: Admin::V1::Campaigns::Projects::IndexSerializer
  end

  private

  def set_campaign
    @campaign = Campaign.find(params[:campaign_id])
  end
end
