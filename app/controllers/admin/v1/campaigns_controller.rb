# frozen_string_literal: true

class Admin::V1::CampaignsController < Admin::V1::BaseController
  before_action -> { authorize! with: Admin::V1::LecuponerPolicy }

  def index
    @campaigns =
      Campaign
        .order(created_at: :desc)
        .page(params[:page])

    render json: @campaigns,
      each_serializer: Admin::V1::Campaigns::IndexSerializer
  end

  def show
    @campaign =
      Campaign
        .find(params[:id])

    render json: @campaign,
      serializer: Admin::V1::Campaigns::ShowSerializer
  end

  def create
    @campaign = Campaign.new(campaign_params)

    if @campaign.save
      render json: @campaign,
        serializer: Admin::V1::Campaigns::CreateSerializer,
        status: :created
    else
      render json: {errors: @campaign.errors.full_messages.join(", ")},
        status: :unprocessable_entity
    end
  end

  private

  def campaign_params
    params.permit(
      :name,
      :target,
      :channel,
      :when_type,
      :when_delivery_date_time,
      :when_repeats_every,
      :when_repeat_type,
      :when_start_time,
      :when_end_by_date,
      :when_end_by_occurrences,
      :content_title,
      :content_body,
      :content_subject,
      :content_sender_name,
      :content_template_name,
      :content_locale,
      :image,
      when_repeat_on_days_of_week: []
    )
  end
end
