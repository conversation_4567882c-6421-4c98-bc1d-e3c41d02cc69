# frozen_string_literal: true

class Admin::V1::Campaigns::CreateSerializer < ActiveModel::Serializer
  attribute :id
  attributes :name,
    :target,
    :channel,
    :status,
    :when_type,
    :when_delivery_date_time,
    :when_repeats_every,
    :when_repeat_type,
    :when_start_time,
    :when_end_by_date,
    :when_end_by_occurrences,
    :when_repeat_on_days_of_week,
    :content_title,
    :content_body,
    :content_subject,
    :content_sender_name,
    :content_template_name,
    :content_locale,
    :image,
    :app_route_id,
    :created_at,
    :updated_at

  def image
    return nil unless object.image.present?

    {
      url: object.image.url
    }
  end
end
