GEM
  remote: https://rubygems.org/
  specs:
    action_policy (0.7.4)
      ruby-next-core (>= 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_model_serializers (0.10.15)
      actionpack (>= 4.1)
      activemodel (>= 4.1)
      case_transform (>= 0.2)
      jsonapi-renderer (>= 0.1.1.beta1, < 0.3)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    acts-as-taggable-on (11.0.0)
      activerecord (>= 7.0, < 8.0)
      zeitwerk (>= 2.4, < 3.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    appsignal (4.5.8)
      logger
      rack (>= 2.0.0)
    ast (2.4.3)
    aws-eventstream (1.3.2)
    aws-partitions (1.1081.0)
    aws-sdk-acm (1.84.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-cloudfront (1.114.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-core (3.222.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-ses (1.82.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.11.0)
      aws-eventstream (~> 1, >= 1.0.2)
    axiom-types (0.1.1)
      descendants_tracker (~> 0.0.4)
      ice_nine (~> 0.11.0)
      thread_safe (~> 0.3, >= 0.3.1)
    backport (1.2.0)
    base64 (0.2.0)
    bcrypt (3.1.20)
    benchmark (0.4.0)
    bigdecimal (3.1.9)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    brakeman (7.0.2)
      racc
    builder (3.3.0)
    bullet (8.0.3)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    bundler-audit (0.9.2)
      bundler (>= 1.2.0, < 3)
      thor (~> 1.0)
    carrierwave (3.0.7)
      activemodel (>= 6.0.0)
      activesupport (>= 6.0.0)
      addressable (~> 2.6)
      image_processing (~> 1.1)
      marcel (~> 1.0.0)
      ssrf_filter (~> 1.0)
    case_transform (0.2)
      activesupport
    charlock_holmes (0.7.9)
    childprocess (5.1.0)
      logger (~> 1.5)
    chronic (0.10.2)
    chunky_png (1.4.0)
    coercible (1.0.0)
      descendants_tracker (~> 0.0.1)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.0)
    coverband (6.1.4)
      redis (>= 3.0)
    cpf_cnpj (1.0.1)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    credit_card_validations (7.0.0)
      activemodel (>= 5.2, < 8.1)
      activesupport (>= 5.2, < 8.1)
    csv (3.3.3)
    date (3.4.1)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    descendants_tracker (0.0.4)
      thread_safe (~> 0.3, >= 0.3.1)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-jwt (0.12.1)
      devise (~> 4.0)
      warden-jwt_auth (~> 0.10)
    devise_invitable (2.0.10)
      actionmailer (>= 5.0)
      devise (>= 4.6)
    diff-lcs (1.6.1)
    docile (1.4.1)
    drb (2.2.1)
    dry-auto_inject (1.1.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-configurable (1.3.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-core (1.1.0)
      concurrent-ruby (~> 1.0)
      logger
      zeitwerk (~> 2.6)
    dry-inflector (1.2.0)
    dry-initializer (3.2.0)
    dry-logic (1.6.0)
      bigdecimal
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-schema (1.14.1)
      concurrent-ruby (~> 1.0)
      dry-configurable (~> 1.0, >= 1.0.1)
      dry-core (~> 1.1)
      dry-initializer (~> 3.2)
      dry-logic (~> 1.5)
      dry-types (~> 1.8)
      zeitwerk (~> 2.6)
    dry-types (1.8.2)
      bigdecimal (~> 3.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0)
      dry-inflector (~> 1.0)
      dry-logic (~> 1.4)
      zeitwerk (~> 2.6)
    erubi (1.13.1)
    ethon (0.16.0)
      ffi (>= 1.15.0)
    excon (1.2.5)
      logger
    factory_bot (6.5.1)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faraday (2.12.2)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    ffaker (2.24.0)
    ffi (1.17.1-arm64-darwin)
    ffi (1.17.1-x86_64-linux-gnu)
    flay (2.13.3)
      erubi (~> 1.10)
      path_expander (~> 1.0)
      ruby_parser (~> 3.0)
      sexp_processor (~> 4.0)
    flog (4.8.0)
      path_expander (~> 1.0)
      ruby_parser (~> 3.1, > 3.1.0)
      sexp_processor (~> 4.8)
    fog-aws (3.30.0)
      base64 (~> 0.2.0)
      fog-core (~> 2.6)
      fog-json (~> 1.1)
      fog-xml (~> 0.1)
    fog-core (2.6.0)
      builder
      excon (~> 1.0)
      formatador (>= 0.2, < 2.0)
      mime-types
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-xml (0.1.5)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    formatador (1.1.0)
    geocoder (1.8.5)
      base64 (>= 0.1.0)
      csv (>= 3.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-cloud-env (2.2.2)
      base64 (~> 0.2)
      faraday (>= 1.0, < 3.a)
    google-logging-utils (0.1.0)
    googleauth (1.14.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    hashdiff (1.1.2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    ice_nine (0.11.2)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jaro_winkler (1.6.0)
    jmespath (1.6.2)
    job-iteration (1.10.0)
      activejob (>= 6.1)
    json (2.10.2)
    jsonapi-renderer (0.2.2)
    jwt (2.10.1)
      base64
    kramdown (2.5.1)
      rexml (>= 3.3.9)
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    language_server-protocol (********)
    launchy (3.1.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
      logger (~> 1.6)
    lint_roller (1.1.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    maintenance_tasks (2.11.0)
      actionpack (>= 7.0)
      activejob (>= 7.0)
      activerecord (>= 7.0)
      csv
      job-iteration (>= 1.3.6)
      railties (>= 7.0)
      zeitwerk (>= 2.6.2)
    marcel (1.0.4)
    mime-types (3.6.2)
      logger
      mime-types-data (~> 3.2015)
    mime-types-data (3.2025.0402)
    mimemagic (0.4.3)
      nokogiri (~> 1)
      rake
    mini_magick (5.2.0)
      benchmark
      logger
    mini_mime (1.1.5)
    minitest (5.25.5)
    msgpack (1.8.0)
    multi_json (1.15.0)
    net-http (0.6.0)
      uri
    net-imap (0.5.7)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    newrelic_rpm (9.18.0)
    nio4r (2.7.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    observer (0.1.2)
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.1)
    parallel (1.26.3)
    parallel_tests (5.1.0)
      parallel
    parser (3.3.7.4)
      ast (~> 2.4.1)
      racc
    path_expander (1.1.3)
    pg (1.5.9)
    pkg-config (1.6.0)
    positioning (0.4.5)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    psych (5.2.3)
      date
      stringio
    public_suffix (6.0.1)
    puma (6.6.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rbs (3.9.2)
      logger
    rdoc (6.13.1)
      psych (>= 4.0.0)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-client (0.24.0)
      connection_pool
    redis-prescription (2.6.0)
    reek (6.5.0)
      dry-schema (~> 1.13)
      logger (~> 1.6)
      parser (~> 3.3.0)
      rainbow (>= 2.0, < 4.0)
      rexml (~> 3.1)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    reverse_markdown (3.0.0)
      nokogiri
    rexml (3.4.1)
    rmagick (6.1.1)
      observer (~> 0.1)
      pkg-config (~> 1.4)
    rotp (6.3.0)
    rqrcode (2.2.0)
      chunky_png (~> 1.0)
      rqrcode_core (~> 1.0)
    rqrcode_core (1.2.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.1.1)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.2)
    rubocop (1.75.2)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.0)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-factory_bot (2.27.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.31.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rspec (3.5.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-rspec_rails (2.31.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
      rubocop-rspec (~> 3.5)
    ruby-next-core (1.1.1)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.3)
      ffi (~> 1.12)
      logger
    ruby_parser (3.21.1)
      racc (~> 1.5)
      sexp_processor (~> 4.16)
    rubycritic (4.9.0)
      flay (~> 2.13)
      flog (~> 4.7)
      launchy (>= 2.5.2)
      parser (>= *******)
      rainbow (~> 3.1.1)
      reek (~> 6.0, < 7.0)
      rexml
      ruby_parser (~> 3.20)
      simplecov (>= 0.22.0)
      tty-which (~> 0.5.0)
      virtus (~> 2.0)
    sd_notify (0.1.1)
    securerandom (0.4.1)
    sexp_processor (4.17.3)
    sidekiq (7.3.9)
      base64
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    sidekiq-throttled (1.5.2)
      concurrent-ruby (>= 1.2.0)
      redis-prescription (~> 2.2)
      sidekiq (>= 6.5)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    solargraph (0.53.4)
      backport (~> 1.2)
      benchmark
      bundler (~> 2.0)
      diff-lcs (~> 1.4)
      jaro_winkler (~> 1.6)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.1)
      logger (~> 1.6)
      observer (~> 0.1)
      ostruct (~> 0.6)
      parser (~> 3.0)
      rbs (~> 3.3)
      reverse_markdown (>= 2.0, < 4)
      rubocop (~> 1.38)
      thor (~> 1.0)
      tilt (~> 2.0)
      yard (~> 0.9, >= 0.9.24)
      yard-solargraph (~> 0.1)
    ssrf_filter (1.2.0)
    statesman (12.0.0)
    store_model (4.2.1)
      activerecord (>= 7.0)
    stringio (3.1.6)
    svix (1.63.1)
    thor (1.3.2)
    thread_safe (0.3.6)
    tilt (2.6.0)
    timeout (0.4.3)
    timezone_finder (1.5.7)
    tty-which (0.5.0)
    typhoeus (1.4.1)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uniform_notifier (1.16.0)
    uri (1.0.3)
    useragent (0.16.11)
    vcr (6.3.1)
      base64
    virtus (2.0.0)
      axiom-types (~> 0.1)
      coercible (~> 1.0)
      descendants_tracker (~> 0.0, >= 0.0.3)
    warden (1.2.9)
      rack (>= 2.0.9)
    warden-jwt_auth (0.11.0)
      dry-auto_inject (>= 0.8, < 2)
      dry-configurable (>= 0.13, < 2)
      jwt (~> 2.1)
      warden (~> 1.2)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    will_paginate (4.0.1)
    yard (0.9.37)
    yard-solargraph (0.1.0)
      yard (~> 0.9)
    zeitwerk (2.7.2)

PLATFORMS
  arm64-darwin-24
  x86_64-linux

DEPENDENCIES
  action_policy
  active_model_serializers (~> 0.10)
  acts-as-taggable-on (~> 11)
  appsignal
  aws-sdk-acm
  aws-sdk-cloudfront
  aws-sdk-ses (~> 1)
  bcrypt
  bootsnap
  brakeman
  bullet
  bundler-audit
  carrierwave (~> 3.0.7)
  charlock_holmes
  coverband
  cpf_cnpj
  credit_card_validations
  debug
  devise
  devise-jwt
  devise_invitable (~> 2.0.0)
  factory_bot_rails
  faraday
  ffaker
  fog-aws
  geocoder
  googleauth
  jwt
  listen (~> 3.2)
  maintenance_tasks
  mimemagic (= 0.4.3)
  mini_magick
  newrelic_rpm
  parallel_tests
  pg
  positioning
  puma (< 7)
  rack-attack
  rack-cors
  rails (~> 7.2.2)
  redis
  rmagick
  rotp
  rqrcode
  rspec-rails (~> 7)
  rubocop
  rubocop-factory_bot
  rubocop-performance
  rubocop-rails
  rubocop-rspec
  rubocop-rspec_rails
  rubycritic
  sd_notify
  sidekiq (< 8)
  sidekiq-throttled
  simplecov
  solargraph
  statesman (~> 12.0.0)
  store_model
  svix (~> 1.24)
  timezone_finder
  typhoeus
  vcr
  webmock
  whenever
  will_paginate

RUBY VERSION
   ruby 3.3.6p108

BUNDLED WITH
   2.6.3
