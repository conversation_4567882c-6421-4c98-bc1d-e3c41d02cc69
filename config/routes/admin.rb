devise_for :admin, class_name: "ClientEmployee", path: "admin/v1", only: [:sessions, :passwords, :invitations], defaults: {format: :json}, controllers: {
  sessions: "admin/v1/sessions",
  invitations: "admin/v1/invitations"
}
devise_scope :admins do
  namespace :admin do
    namespace :v1 do
      resources :campaigns, only: [:index, :show, :create] do
        resources :projects, only: :index, controller: "campaigns/projects"
        collection do
          resources :groups, only: :index, controller: "campaigns/groups"
        end
      end

      namespace :coupon_redeems do
        resources :branches, only: :index do
          resources :coupons, only: :index, controller: "branches/coupons"
        end
        resource :coupons, only: :none do
          resources :redemm, only: :create
        end
        resources :orders, only: [:index, :create]
        resources :projects, only: :index
      end

      resources :projects, only: [:index, :show] do
        resource :mailer_configs, only: :show, controller: "projects/mailer_configs"
      end

      namespace :purchase_imports do
        resources :branches, only: :none do
          resources :promotions, only: :index, controller: "branches/promotions"
        end
        resources :import_files, only: [:index, :show, :create] do
          resources :errors, only: :index, controller: "import_files/errors"
          resources :error_exports, only: :create, controller: "import_files/error_exports"
        end
        resources :organizations, only: :index do
          resources :branches, only: :index, controller: "organizations/branches"
        end
      end

      namespace :member_referrals do
        resources :activate, only: :create
        resources :leads, only: [:index, :update] do
          collection do
            resources :entry_types, only: :index, controller: "leads/entry_types"
            resources :statuses, only: :index, controller: "leads/statuses"
          end
        end
        resource :configs, only: [:show, :update]
      end

      namespace :finances do
        resources :clearings, only: [:index], controller: "clearings"
        resources :payouts, only: [:index, :destroy], controller: "payouts"
        resources :payout_batches, only: [:index, :create], controller: "payout_batches" do
          collection do
            resource :download, only: [:create], controller: "payout_batches/download"
            resource :upload, only: [:create], controller: "payout_batches/upload"
          end
        end
      end

      namespace :navigations do
        resources :banners
        resources :banner_providers, only: :index
        resources :branches, only: :none do
          resources :promotions, only: :index, controller: "branches/promotions"
        end
        resources :categories, only: :index
        resources :geolocations, only: :index do
          get :info, to: "geolocations#show", on: :collection
        end
        resources :organizations, only: :index do
          resources :branches, only: :index, controller: "organizations/branches"
        end
        patch "/:id/position", to: "position#update", constraints: {id: /\d.+/}
      end
    end
  end
end
