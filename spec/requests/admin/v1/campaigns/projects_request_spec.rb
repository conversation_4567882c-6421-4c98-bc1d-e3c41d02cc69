# frozen_string_literal: true

require "rails_helper"
require "action_policy/rspec"

RSpec.describe Admin::V1::Campaigns::ProjectsController, type: :request do
  let(:response_hash) { JSON.parse(response.body) }

  describe "#index" do
    let!(:campaign) { create(:campaign, :content_to_when_type_now) }
    let!(:active_businesses) { create_list(:business, 3, active: true) }
    let!(:inactive_business) { create(:business, status: Business::Status::INACTIVE) }

    let(:serialized_keys) do
      %w[id name cnpj]
    end

    before do
      # Associate active businesses with campaign
      active_businesses.each do |business|
        create(:businesses_campaign, campaign: campaign, business: business)
      end

      # Associate inactive business with campaign
      create(:businesses_campaign, campaign: campaign, business: inactive_business)
    end

    context "when admin is not authenticated" do
      it "must be unauthorized" do
        get "/admin/v1/campaigns/#{campaign.id}/projects"
        expect(response).to be_unauthorized
      end
    end

    context "when admin is authenticated" do
      let(:admin) { create(:client_employee, :admin_lecupon) }
      let(:headers) { Devise::JWT::TestHelpers.auth_headers({}, admin) }

      context "when lecupon admin" do
        it "returns only active businesses associated with the campaign" do
          get("/admin/v1/campaigns/#{campaign.id}/projects", headers:)

          expect(response).to be_successful
          expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
          expect(response_hash.count).to eq(3)

          returned_ids = response_hash.map { |project| project["id"] }
          expected_ids = active_businesses.pluck(:id)

          expect(returned_ids).to match_array(expected_ids)
          expect(returned_ids).not_to include(inactive_business.id)
        end

        it "returns businesses ordered by name" do
          get("/admin/v1/campaigns/#{campaign.id}/projects", headers:)

          expect(response).to be_successful

          returned_names = response_hash.map { |project| project["name"] }
          expected_names = active_businesses.map(&:name).sort

          expect(returned_names).to eq(expected_names)
        end

        context "with pagination" do
          before do
            # Create more businesses to test pagination
            additional_businesses = create_list(:business, 30, active: true)
            additional_businesses.each do |business|
              create(:businesses_campaign, campaign: campaign, business: business)
            end
          end

          it "returns paginated results" do
            get("/admin/v1/campaigns/#{campaign.id}/projects", headers:, params: {page: 2})

            expect(response).to be_successful
            expect(response_hash.count).to be <= 25 # Default Kaminari per_page
          end
        end

        context "when campaign has no associated businesses" do
          let!(:empty_campaign) { create(:campaign, :content_to_when_type_now) }

          it "returns empty array" do
            get("/admin/v1/campaigns/#{empty_campaign.id}/projects", headers:)

            expect(response).to be_successful
            expect(response_hash).to eq([])
          end
        end
      end

      context "when campaign does not exist" do
        it "returns not found" do
          get("/admin/v1/campaigns/999999/projects", headers:)

          expect(response).to have_http_status(:not_found)
        end
      end
    end
  end
end
