# frozen_string_literal: true

require "rails_helper"
require "action_policy/rspec"

RSpec.describe Admin::V1::CampaignsController, type: :request do
  let(:response_hash) { JSON.parse(response.body) }

  describe "#index" do
    let(:serialized_keys) do
      %w[id name target channel status when_type when_delivery_date_time when_start_time
         content_title synced_at created_at updated_at]
    end

    context "when admin is not authenticated" do
      it "must be unauthorized" do
        get "/admin/v1/campaigns"
        expect(response).to be_unauthorized
      end
    end

    context "when admin is authenticated" do
      let(:admin) { create(:client_employee, :admin_lecupon) }
      let(:headers) { Devise::JWT::TestHelpers.auth_headers({}, admin) }

      context "when lecupon admin" do
        let!(:campaigns) { create_list(:campaign, 3, :content_to_when_type_now) }

        it "must return all campaigns" do
          get("/admin/v1/campaigns", headers:)

          expect(response).to be_successful
          expect(response_hash.map(&:keys)).to all(match_array(serialized_keys))
          expect(response_hash.count).to eq(Campaign.count)
        end

        it "returns campaigns ordered by created_at desc" do
          get("/admin/v1/campaigns", headers:)

          expect(response).to be_successful

          returned_ids = response_hash.map { |campaign| campaign["id"] }
          expected_ids = Campaign.order(created_at: :desc).pluck(:id)

          expect(returned_ids).to eq(expected_ids)
        end

        context "with pagination" do
          before do
            create_list(:campaign, 30, :content_to_when_type_now)
          end

          it "returns paginated results" do
            get("/admin/v1/campaigns", headers:, params: {page: 2})

            expect(response).to be_successful
            expect(response_hash.count).to be <= 25 # Default Kaminari per_page
          end
        end
      end

      context "when business admin" do
        let(:business) { create(:business) }
        let(:admin) { create(:client_employee, :admin_lecupon) }
        let!(:campaigns) { create_list(:campaign, 2, :content_to_when_type_now) }

        it "must return all campaigns" do
          get("/admin/v1/campaigns", headers:)

          expect(response).to be_successful
          expect(response_hash.count).to eq(Campaign.count)
        end
      end
    end
  end

  describe "#show" do
    let!(:campaign) { create(:campaign, :content_to_when_type_later) }
    let!(:app_route) { create(:app_route) }

    let(:serialized_keys) do
      %w[id name target channel status when_type when_delivery_date_time when_repeats_every
         when_repeat_type when_start_time when_end_by_date when_end_by_occurrences
         when_repeat_on_days_of_week content_title content_body content_subject
         content_sender_name content_template_name content_locale image
         synced_at created_at updated_at]
    end

    context "when admin is not authenticated" do
      it "must be unauthorized" do
        get "/admin/v1/campaigns/#{campaign.id}"
        expect(response).to be_unauthorized
      end
    end

    context "when admin is authenticated" do
      let(:admin) { create(:client_employee, :admin_lecupon) }
      let(:headers) { Devise::JWT::TestHelpers.auth_headers({}, admin) }

      context "when campaign exists" do
        it "must return campaign details" do
          get("/admin/v1/campaigns/#{campaign.id}", headers:)

          expect(response).to be_successful
          expect(response_hash.keys).to match_array(serialized_keys)
          expect(response_hash["id"]).to eq(campaign.id)
          expect(response_hash["name"]).to eq(campaign.name)
        end

        context "when campaign has image" do
          let(:image_file) { Tempfile.new(["campaign", ".png"], "tmp") }

          before do
            campaign.update!(image: image_file)
          end

          it "returns image with versions" do
            get("/admin/v1/campaigns/#{campaign.id}", headers:)

            expect(response).to be_successful
            expect(response_hash["image"]).to be_present
            expect(response_hash["image"]).to have_key("url")
          end
        end
      end

      context "when campaign does not exist" do
        it "must return not found" do
          get("/admin/v1/campaigns/999999", headers:)
          expect(response).to have_http_status(:not_found)
        end
      end
    end
  end

  describe "#create" do
    let(:valid_params) do
      {
        name: "Test Campaign",
        target: "event",
        channel: "push",
        when_type: "now",
        content_title: "Test Title",
        content_body: "Test Body",
        content_subject: "Test Subject",
        content_sender_name: "Test Sender",
        content_template_name: "Test Template",
        content_locale: "pt_BR"
      }
    end

    let(:serialized_keys) do
      %w[id name target channel status when_type when_delivery_date_time when_repeats_every
         when_repeat_type when_start_time when_end_by_date when_end_by_occurrences
         when_repeat_on_days_of_week content_title content_body content_subject
         content_sender_name content_template_name content_locale image
         created_at updated_at]
    end

    context "when admin is not authenticated" do
      it "must be unauthorized" do
        post "/admin/v1/campaigns", params: valid_params
        expect(response).to be_unauthorized
      end
    end

    context "when admin is authenticated" do
      let(:admin) { create(:client_employee, :admin_lecupon) }
      let(:headers) { Devise::JWT::TestHelpers.auth_headers({}, admin) }

      context "with valid parameters" do
        it "creates a new campaign" do
          expect do
            post("/admin/v1/campaigns", headers:, params: valid_params)
          end.to change(Campaign, :count).by(1)

          expect(response).to have_http_status(:created)
          expect(response_hash.keys).to match_array(serialized_keys)
          expect(response_hash["name"]).to eq("Test Campaign")
          expect(response_hash["status"]).to eq("draft")
        end

        it "sets default values correctly" do
          post("/admin/v1/campaigns", headers:, params: valid_params)

          expect(response).to have_http_status(:created)
          expect(response_hash["target"]).to eq("event")
          expect(response_hash["channel"]).to eq("push")
          expect(response_hash["status"]).to eq("draft")
        end

        context "with optional parameters" do
          let(:params_with_optional) do
            valid_params.deep_merge(
              when_delivery_date_time: 1.day.from_now.iso8601,
              when_repeat_on_days_of_week: ["monday", "tuesday"]
            )
          end

          it "creates campaign with optional parameters" do
            post("/admin/v1/campaigns", headers:, params: params_with_optional)

            expect(response).to have_http_status(:created)
            expect(response_hash["when_repeat_on_days_of_week"]).to eq(["monday", "tuesday"])
          end
        end
      end

      context "with invalid parameters" do
        let(:invalid_params) do
          {
            name: "",
            target: "invalid_target"
          }
        end

        it "returns validation errors" do
          post("/admin/v1/campaigns", headers:, params: invalid_params)

          expect(response).to have_http_status(:unprocessable_entity)
          expect(response_hash).to have_key("errors")
          expect(response_hash["errors"]).to be_present
        end

        it "does not create a campaign" do
          expect do
            post("/admin/v1/campaigns", headers:, params: invalid_params)
          end.not_to change(Campaign, :count)
        end
      end
    end
  end
end
